<template>
  <el-dialog
    :title="isEditMode ? $T('编辑设备') : $T('新增设备')"
    :visible.sync="dialogVisible"
    width="640px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleDialogClose"
  >
    <div class="dialog-content">
      <el-form
        ref="addDeviceForm"
        :model="formData"
        :rules="formRules"
        label-position="top"
      >
        <el-row :gutter="30">
          <el-col :span="12">
            <!-- 设备名称 -->
            <el-form-item :label="$T('设备名称')" prop="deviceName" required>
              <el-input
                v-model="formData.deviceName"
                :placeholder="$T('请输入内容')"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <!-- 设备类型 -->
            <el-form-item :label="$T('设备类型')" prop="deviceType" required>
              <el-select
                v-model="formData.deviceType"
                :placeholder="$T('请选择')"
                style="width: 100%"
                :disabled="isEditMode"
                @change="handleDeviceTypeChange"
              >
                <el-option
                  v-for="item in deviceTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="30">
          <el-col :span="12">
            <!-- 管网设备 -->
            <el-form-item :label="$T('管网设备')" prop="networkDevice" required>
              <div class="network-device-input">
                <el-input
                  v-model="formData.networkDevice"
                  :placeholder="$T('请选择')"
                  readonly
                />
                <i
                  class="el-icon-edit network-device-icon"
                  @click="selectNetworkDevice"
                ></i>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div slot="footer">
      <el-button plain @click="handleCancel">{{ $T("取消") }}</el-button>
      <el-button type="success" @click="handleConfirm">
        {{ $T("确定") }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getEnumOptions } from "@/utils/enumManager";

export default {
  name: "AddEditDeviceDialog",
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    isEditMode: {
      type: Boolean,
      default: false
    },
    deviceData: {
      type: Object,
      default: () => ({})
    },
    selectedNetworkDevices: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      dialogVisible: false,
      deviceTypeOptions: [],
      formData: {
        deviceName: "",
        deviceType: "",
        networkDevice: "",
        networkDeviceId: "",
        networkDeviceModelLabel: ""
      },
      formRules: {
        deviceName: [
          {
            required: true,
            message: $T("请输入设备名称"),
            trigger: "blur"
          },
          {
            max: 50,
            message: $T("设备名称不能超过50个字符"),
            trigger: "blur"
          }
        ],
        deviceType: [
          {
            required: true,
            message: $T("请选择设备类型"),
            trigger: "change"
          }
        ],
        networkDevice: [
          {
            required: true,
            message: $T("请选择管网设备"),
            trigger: "change"
          }
        ]
      }
    };
  },
  watch: {
    visible: {
      handler(newVal) {
        this.dialogVisible = newVal;
        if (newVal) {
          this.initFormData();
        }
      },
      immediate: true
    },
    dialogVisible(newVal) {
      this.$emit("update:visible", newVal);
    },
    selectedNetworkDevices: {
      handler(devices) {
        this.updateNetworkDeviceDisplay(devices);
      },
      deep: true
    }
  },
  created() {
    this.loadDeviceTypeOptions();
  },
  methods: {
    // 加载设备类型选项
    loadDeviceTypeOptions() {
      this.deviceTypeOptions = getEnumOptions("VPP_DEVICE_TYPE");
    },

    // 初始化表单数据
    initFormData() {
      if (this.isEditMode && this.deviceData) {
        this.formData = {
          deviceName: this.deviceData.deviceName || "",
          deviceType: this.deviceData.deviceType || "",
          networkDevice: "",
          networkDeviceId: "",
          networkDeviceModelLabel: ""
        };
      } else {
        this.resetForm();
      }
      this.updateNetworkDeviceDisplay(this.selectedNetworkDevices);
    },

    // 重置表单
    resetForm() {
      this.formData = {
        deviceName: "",
        deviceType: "",
        networkDevice: "",
        networkDeviceId: "",
        networkDeviceModelLabel: ""
      };

      this.$nextTick(() => {
        if (this.$refs.addDeviceForm) {
          this.$refs.addDeviceForm.clearValidate();
        }
      });
    },

    // 更新管网设备显示
    updateNetworkDeviceDisplay(devices) {
      if (!devices || devices.length === 0) {
        this.formData.networkDevice = "";
        return;
      }

      const deviceNames = devices.map(device => device.name);
      this.formData.networkDevice =
        deviceNames.length > 3
          ? deviceNames.slice(0, 3).join(", ") +
            $T(" 等 {0} 个设备", deviceNames.length - 3)
          : deviceNames.join(", ");
    },

    // 设备类型改变处理
    handleDeviceTypeChange(deviceType) {
      // 清空之前选择的管网设备
      this.formData.networkDevice = "";
      this.formData.networkDeviceId = "";

      // 通知父组件设备类型改变
      this.$emit("device-type-change", deviceType);
    },

    // 选择管网设备
    selectNetworkDevice() {
      if (!this.formData.deviceType) {
        this.$message.warning($T("请先选择设备类型"));
        return;
      }

      this.$emit("select-network-device");
    },

    // 弹窗关闭处理
    handleDialogClose() {
      this.resetForm();
      this.$emit("close");
    },

    // 取消按钮处理
    handleCancel() {
      this.dialogVisible = false;
    },

    // 确定按钮处理
    handleConfirm() {
      this.$refs.addDeviceForm.validate(valid => {
        if (valid) {
          this.$emit("confirm", this.formData);
        } else {
          this.$message.error($T("请检查输入信息"));
        }
      });
    }
  }
};
</script>

<style scoped>
.dialog-content {
  background: var(--BG1);
}

.network-device-input {
  position: relative;
}

.network-device-icon {
  position: absolute;
  right: var(--J1);
  top: 50%;
  transform: translateY(-50%);
  font-size: var(--J2);
  color: var(--T3);
  cursor: pointer;
  z-index: 10;
}

/* 表单样式 */
.dialog-content ::v-deep .el-form-item__label {
  color: var(--T1);
  font-size: var(--Aa);
  font-weight: 400;
  line-height: var(--J3);
  padding-bottom: var(--J1);
}

.dialog-content ::v-deep .el-form-item__label::before {
  content: "*";
  color: var(--Sta3);
  margin-right: var(--J0);
}

.dialog-content ::v-deep .el-input__inner {
  background: var(--BG1);
  border: 1px solid var(--B1);
  color: var(--T1);
  font-size: var(--Aa);
  border-radius: var(--Ra);
  height: 40px;
  line-height: 40px;
}

.dialog-content ::v-deep .el-input__inner:focus {
  border-color: var(--ZS);
}

.dialog-content ::v-deep .el-input__inner::placeholder {
  color: var(--T4);
}

.dialog-content ::v-deep .el-select .el-input__inner {
  cursor: pointer;
}

/* 弹窗样式 */
::v-deep .el-dialog {
  background: var(--BG1);
  border-radius: var(--Ra1);
  overflow: hidden;
}

::v-deep .el-dialog__header {
  background: var(--BG1);
  padding: var(--J3) var(--J4) var(--J2) var(--J4);
  position: relative;
}

::v-deep .el-dialog__title {
  color: var(--T1);
  font-size: var(--Ac);
  font-weight: 600;
}

::v-deep .el-dialog__headerbtn {
  position: absolute;
  top: var(--J3);
  right: var(--J3);
}

::v-deep .el-dialog__headerbtn .el-dialog__close {
  font-size: var(--Ac);
  color: var(--T3);
}

::v-deep .el-dialog__body {
  background: var(--BG1);
  padding: var(--J1) var(--J4) var(--J3) var(--J4);
}

::v-deep .el-dialog__footer {
  background: var(--BG1);
  padding: var(--J2) var(--J4) var(--J3) var(--J4);
  text-align: right;
}
</style>
